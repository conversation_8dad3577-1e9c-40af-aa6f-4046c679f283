//
//  ContentView.swift
//  iOSProject
//
//  Created by Apple on 08/08/2025.
//

import SwiftUI

struct MainView: View {
    @EnvironmentObject var themeManager: ThemeManager

    @EnvironmentObject var dependencyContainer: DependencyContainer
    @StateObject private var navigationService = NavigationService()

    var body: some View {
        Group {
            switch navigationService.currentView {
            case .authentication:
                NavigationStack {
                    AuthenticationView()
                        .environmentObject(navigationService)
                }
            case .mainNavigation:
                MainNavigationView()
            }
        }
       
        .themedBackground()
        .environmentObject(navigationService)
    }
}

#Preview {
    MainView()
        .environmentObject(ThemeManager())
        .environmentObject(DependencyContainer())
}
