import SwiftUI

// MARK: - Main Navigation View

struct MainNavigationView: View {
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            LandingView()
                .tabItem {
                    Image(.houseIcon)
                        .renderingMode(.template)
                        .resizable()
                    
                    Text("Home")
                }
                .tag(0)

            FavouriteView()
                .tabItem {
                    Image(.heartIcon)
                        .renderingMode(.template)
                        .resizable()
                    Text("Favourite")
                }
                .tag(1)

            NewsView()
                .tabItem {
                    Image(.houseIcon)
                        .renderingMode(.template)
                        .resizable()
                    Text("News")
                }
                .tag(2)

            NotificationView()
                .tabItem {
                    Image(.bellIcon)
                        .renderingMode(.template)
                        .resizable()
                    Text("Notification")
                }
                .tag(3)
        }
    }

}

// MARK: - Placeholder Views

// LandingView is now in its own file

struct FavouriteView: View {
    var body: some View {
        VStack {
            Text("Favourite Screen")
                .headline24SemiBold()
            Text("Favourite content will be implemented here")
                .body14Regular()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .themedBackground()
    }
}

struct NewsView: View {
    var body: some View {
        VStack {
            Text("News Screen")
                .headline24SemiBold()
            Text("News content will be implemented here")
                .body14Regular()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .themedBackground()
    }
}

struct NotificationView: View {
    var body: some View {
        VStack {
            Text("Notification Screen")
                .headline24SemiBold()
            Text("Notification content will be implemented here")
                .body14Regular()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .themedBackground()
    }
}



// MARK: - Preview

struct MainNavigationView_Previews: PreviewProvider {
    static var previews: some View {
        MainNavigationView()
    }
}
