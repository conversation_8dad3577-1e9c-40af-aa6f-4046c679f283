import SwiftUI

// MARK: - Button Variant
enum CustomButtonVariant {
    case filled
    case outlined
}

// MARK: - Custom Button
struct CustomButton: View {
    let title: String
    let variant: CustomButtonVariant
    let isLoading: Bool
    let leadingIcon: String?
    let action: () -> Void
    
    @Environment(\.appColors) var colors
    
    init(
        title: String,
        variant: CustomButtonVariant = .filled,
        isLoading: Bool = false,
        leadingIcon: String? = nil,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.variant = variant
        self.isLoading = isLoading
        self.leadingIcon = leadingIcon
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8.h) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: textColor))
                        .scaleEffect(0.8)
                } else {
                    if let leadingIcon = leadingIcon {
                        // Try to load as asset image first, fallback to system image
                        if UIImage(named: leadingIcon) != nil {
                            Image(leadingIcon)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 16, height: 16)
                                .foregroundColor(textColor)
                        } else {
                            Image(systemName: leadingIcon)
                                .font(.system(size: 16))
                                .foregroundColor(textColor)
                        }
                    }

                    Text(title)
                        .title16Medium()
                        .foregroundColor(textColor)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 44.h)
            .background(backgroundColor)
            .overlay(
                RoundedRectangle(cornerRadius: 8.h)
                    .stroke(borderColor, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: 8.h))
        }
        .disabled(isLoading)
    }
    
    private var backgroundColor: Color {
        switch variant {
        case .filled:
            return colors.brandPrimary
        case .outlined:
            return colors.surface
        }
    }
    
    private var textColor: Color {
        switch variant {
        case .filled:
            return colors.onPrimary
        case .outlined:
            return colors.primaryText
        }
    }
    
    private var borderColor: Color {
        switch variant {
        case .filled:
            return colors.brandPrimary
        case .outlined:
            return colors.borderDefault
        }
    }
}

// MARK: - Preview
struct CustomButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            CustomButton(
                title: "Log in",
                action: { }
            )
            
            CustomButton(
                title: "Log in with Google",
                variant: .outlined,
                leadingIcon: "globe",
                action: { }
            )
            
            CustomButton(
                title: "Loading...",
                isLoading: true,
                action: { }
            )
        }
        .padding()
        .environmentObject(ThemeManager())
    }
}
