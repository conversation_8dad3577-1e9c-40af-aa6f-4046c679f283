import SwiftUI
import Foundation

// MARK: - Navigation Service
class NavigationService: NavigationServiceProtocol, ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentView: AppView = .authentication
    @Published var navigationPath = NavigationPath()
    
    // MARK: - Navigation Methods
    func navigateToMainNavigation() {
        DispatchQueue.main.async {
            self.currentView = .mainNavigation
            self.navigationPath = NavigationPath()
        }
    }
    
    func navigateToAuthentication() {
        DispatchQueue.main.async {
            self.currentView = .authentication
            self.navigationPath = NavigationPath()
        }
    }
    
    func navigateBack() {
        DispatchQueue.main.async {
            if !self.navigationPath.isEmpty {
                self.navigationPath.removeLast()
            }
        }
    }
    
    // MARK: - Additional Navigation Methods
    func navigateToProfile() {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.profile)
        }
    }
    
    func navigateToSettings() {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.settings)
        }
    }
    
    func navigateToNewsDetail(newsId: String) {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.newsDetail(id: newsId))
        }
    }
    
    func navigateToNotifications() {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.notifications)
        }
    }
    
    func popToRoot() {
        DispatchQueue.main.async {
            self.navigationPath = NavigationPath()
        }
    }
    
    func canGoBack() -> Bool {
        return !navigationPath.isEmpty
    }
}

// MARK: - App View Enum
enum AppView {
    case authentication
    case mainNavigation
}

// MARK: - App Destination Enum
enum AppDestination: Hashable {
    case profile
    case settings
    case newsDetail(id: String)
    case notifications
    case favourites
    
    // MARK: - Hashable Conformance
    func hash(into hasher: inout Hasher) {
        switch self {
        case .profile:
            hasher.combine("profile")
        case .settings:
            hasher.combine("settings")
        case .newsDetail(let id):
            hasher.combine("newsDetail")
            hasher.combine(id)
        case .notifications:
            hasher.combine("notifications")
        case .favourites:
            hasher.combine("favourites")
        }
    }
    
    static func == (lhs: AppDestination, rhs: AppDestination) -> Bool {
        switch (lhs, rhs) {
        case (.profile, .profile),
             (.settings, .settings),
             (.notifications, .notifications),
             (.favourites, .favourites):
            return true
        case (.newsDetail(let lhsId), .newsDetail(let rhsId)):
            return lhsId == rhsId
        default:
            return false
        }
    }
}
