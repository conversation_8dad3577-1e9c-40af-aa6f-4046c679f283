---
type: "manual"
---

# 📘 Flutter Senior Developer Implementation Guideline (Clean Architecture + BLoC)

## 🎯 Project Goal
Build a 2–3 screen Flutter app that demonstrates senior-level software design using **Clean Architecture** and the **BLoC pattern** for state management and business logic separation — without requiring real API integration.

---

## 🧠 Architecture & Code Structure

### ✅ Clean Architecture Layers
Organize the project as:
```
/lib
  /core             → Common utilities, constants, error handling
  /features
    /<feature_name>
      /data         → Data sources, model adapters
      /domain       → Entities, repository interfaces, use cases
      /presentation → UI widgets, BLoC, events, states
  /di               → Dependency injection (e.g., service locator)
```

### ✅ BLoC + Clean Architecture Flow
1. **UI (Widgets)** interacts with
2. **BLoC (Event → State)** which invokes
3. **UseCase (Application Logic)** which communicates with
4. **Repository Interface** implemented by
5. **DataSource (Mock)**

> Example:
```
UI → Event → BLoC → UseCase → Repository → MockData
```

---

## 🔁 State Management with BLoC

### ✅ Use `flutter_bloc` for Event-State driven architecture
- Events trigger logic
- States represent UI-ready data
- BLoC should be pure — no UI logic

```bash
flutter pub add flutter_bloc equatable
```

---

## 🔌 Dependency Injection

### ✅ Use `get_it` for DI
- Register use cases, repositories, and blocs
- Keep dependency configuration in `/di/injection.dart`

---

## 🗃 Repository Pattern

### ✅ Define repository contracts in domain
```dart
abstract class ProductRepository {
  Future<List<Product>> getAllProducts();
}
```

- Implement mock data inside `data/repositories/`
- Simulate network latency using `Future.delayed`

---

## ⚙️ Use Cases

- Each use case represents a single business operation
```dart
class GetAllProducts {
  final ProductRepository repository;
  GetAllProducts(this.repository);

  Future<List<Product>> call() => repository.getAllProducts();
}
```

---

## 📱 UI / UX Guidelines

- Use Material Design 3
- Build a responsive UI with:
  - `BlocBuilder` for reactive updates
  - `BlocListener` for actions
- Handle states:
  - `LoadingState` → CircularProgressIndicator
  - `LoadedState` → ListView
  - `EmptyState` → Placeholder
  - `ErrorState` → Retry UI

---

## 🧪 Testing Strategy

- Test each layer independently:
  - BLoC: Test events & state transitions using `bloc_test`
  - UseCases: Test business rules
  - Repository: Mocked implementations
```bash
flutter pub add bloc_test mocktail --dev
```

---

## 📄 Project Setup & Conventions

- Use:
  - `flutter_bloc`, `equatable`, `bloc_test`, `get_it`
- Code Formatting: Enable `flutter_lints`
- Folder Naming: `snake_case`
- File Naming: Match class names

---

## 📄 README.md Must Include

- App purpose
- Clean Architecture overview
- Folder structure
- Run instructions
- Features and screenshots
- "What would be added if more time"

---

## ✅ Code Quality Checklist

- ✅ No UI logic in BLoC
- ✅ No logic in widgets
- ✅ All constants in a central config
- ✅ Proper separation between data/domain/presentation
- ✅ Modular and scalable structure

---

## 🚫 Anti-Patterns to Avoid

- ❌ Business logic inside widgets
- ❌ Tightly coupled layers
- ❌ Global state/singletons without DI
- ❌ UI depending directly on repository

---

## ✅ Deliverables

- Full Flutter project with:
  - Clean Architecture
  - BLoC for state management
  - Modular codebase with testable components
  - Mocked data source
- GitHub repo with clear commits and README

---

## 🧠 Bonus (Optional but Impressive)

- Dark mode support
- BlocObserver for logging/debugging
- App-wide error handling (via `Failure` model)
- Widget and integration tests