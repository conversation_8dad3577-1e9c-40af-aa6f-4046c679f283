// lib/core/utils/image_constant.dart
class ImageConstant {
  // Base path for all assets
  static final String _basePath = 'assets/images/';

  // Placeholder image for fallback
  static String placeholder = '${_basePath}placeholder.png';

  // Authentication Screen
  static String googleLogo = '${_basePath}214_1922.svg';
  static String appleLogo = '${_basePath}214_1929.svg';
  static String headerImgIllustration = '${_basePath}Illustration.svg';
  static String email = '${_basePath}img_mail.svg';

  // Model Authentication Model Screen
  // Model Profile Model Screen
  static String defaultProfileAvatar = '${_basePath}profile_picture.png';

  // Dark mode icon
  static String imgDarkMode = '${_basePath}img_dark_mode.svg';

  // Profile Screen
  static String arrowRightIcon = '${_basePath}img_arrowright.svg';
  static String imgIcedit = '${_basePath}img_icedit.svg';
  static String imgIcwallet = '${_basePath}img_icwallet.svg';

  // Profile Screen Screen
  static String profileImagePlaceholder = '${_basePath}img_ellipse.png';
  static String imgIccontactSupport = '${_basePath}img_iccontact_support.svg';
  static String imgIcexchangeMessage = '${_basePath}img_icexchange_message.svg';
  static String imgIcinfo = '${_basePath}img_icinfo.svg';
  static String imgIclogout = '${_basePath}img_iclogout.svg';
  static String imgIcmarketStatus = '${_basePath}img_icmarket_status.svg';
  static String imgIcpledge = '${_basePath}img_icpledge.svg';
  static String imgIcreports = '${_basePath}img_icreports.svg';
  static String imgIcsettings = '${_basePath}img_icsettings.svg';
  static String imgIcticket = '${_basePath}img_icticket.svg';
  static String imgIctimer = '${_basePath}img_ictimer.svg';
  static String imgSearch = '${_basePath}img_search.svg';
  static String arrowLeftIcon = '${_basePath}img_arrowleft.svg';
  static String imgEditImage = '${_basePath}img_edit_image.svg';
  static String imgUser = '${_basePath}img_user.svg';
  static String imgSearchGray900_0c = '${_basePath}img_search_gray_900_0c.svg';
  static String imgHistory = '${_basePath}img_history.svg';
  static String imgComplain = '${_basePath}img_complain.svg';
  static String imgReferral = '${_basePath}img_referral.svg';
  static String imgAboutUs = '${_basePath}img_about_us.svg';
  static String imgSettings = '${_basePath}img_settings.svg';
  static String darkMode = '${_basePath}darkmode_icon.svg';
  static String imgHelpAndSupport = '${_basePath}img_help_and_support.svg';
  static String imgLogout = '${_basePath}img_logout.svg';

  // Landing Screen
  static String imgPiechart = '${_basePath}img_piechart.svg';
  static String imgZap = '${_basePath}img_zap.svg';
  static String imgSmartphone = '${_basePath}img_smartphone.svg';
  static String imgUsers = '${_basePath}img_users.svg';
  static String imgLogomark = '${_basePath}logo.svg';
  static String imgMenu = '${_basePath}img_menu.svg';
  static String imgArrowrightDeepPurple300 = '${_basePath}img_arrowright_deep_purple_300.svg';
  static String imgAppleLogo = '${_basePath}img_apple_logo.svg';
  static String imgDownloadOnThe = '${_basePath}img_download_on_the.svg';
  static String imgAppStore = '${_basePath}img_app_store.svg';
  static String imgGooglePlayLogo = '${_basePath}google_play_logo.svg';
  static String imgGetItOn = '${_basePath}img_get_it_on.svg';
  static String imgGooglePlay = '${_basePath}img_google_play.svg';
  static String imgBlob = '${_basePath}img_blob.png';
  static String imgScreenMockupReplaceFill = '${_basePath}img_screen_mockup_replace_fill.png';
  static String imgIphoneMockup = '${_basePath}img_iphone_mockup.svg';
  static String img941 = '${_basePath}img_941.svg';
  static String imgRight = '${_basePath}img_right.svg';
  static String phone_mockup1 = '${_basePath}phone_mockup1.png';
  static String phone_mockup2 = '${_basePath}phone_mockup2.png';
  static String phone_mockup3 = '${_basePath}phone_mockup3.png';
  static String phone_mockup4 = '${_basePath}phone_mockup4.png';
  static String imgVector = '${_basePath}img_vector.svg';
  static String imgVectorGreenA700 = '${_basePath}img_vector_green_a700.png';
  static String imgCompanyLogo = '${_basePath}img_company_logo.svg';
  static String imgVectorBlueA700_01 = '${_basePath}img_vector_blue_a700_01.svg';
  static String imgVectorBlack900 = '${_basePath}img_vector_black_900.svg';
  static String imgVectorGray900_03 = '${_basePath}img_vector_gray_900_03.svg';
  static String imgVectorBlueA400 = '${_basePath}img_vector_blue_a400.svg';
  static String imgScreenMockupReplaceFill529x254 = '${_basePath}img_screen_mockup_replace_fill_529x254.png';
  static String img941Black900 = '${_basePath}img_941_black_900.svg';
  static String imgRightBlack900 = '${_basePath}img_right_black_900.svg';
  static String imgGooglePlayLogoWhiteA700 = '${_basePath}img_google_play_logo_white_a700.svg';
  static String imgScreenMockupReplaceFill500x240 = '${_basePath}img_screen_mockup_replace_fill_500x240.png';
  static String imgScreenMockupReplaceFill1 = '${_basePath}img_screen_mockup_replace_fill_1.png';
  static String imgScreenMockupReplaceFill2 = '${_basePath}img_screen_mockup_replace_fill_2.png';
  static String imgHouse = '${_basePath}img_house.svg';
  static String imgHeart = '${_basePath}img_heart.svg';
  static String imgNotepad = '${_basePath}img_notepad.svg';
  static String imgBellsimple = '${_basePath}img_bellsimple.svg';

  // Custom Image View Screen
  static String imgImageNotFound = '${_basePath}image_not_found.png';

  // Profile Menu Item Widget Screen

  // Phone Mockup Widget Screen
  static String imgButtons = '${_basePath}img_buttons.svg';
  static String imgDeviceSurround = '${_basePath}img_device_surround.svg';
  static String imgHighlightBand = '${_basePath}img_highlight_band.svg';
  static String imgBackground = '${_basePath}img_background.svg';
  static String imgAntennaBands = '${_basePath}img_antenna_bands.svg';
  static String imgSpeaker = '${_basePath}img_speaker.svg';
  static String imgCamera = '${_basePath}img_camera.svg';

  // Pricing Card Widget Screen
  static String imgCheckIcon = '${_basePath}img_check_icon.svg';
  static String imgVectors = '${_basePath}img_vectors.svg';
}
