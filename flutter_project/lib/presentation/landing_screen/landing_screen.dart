import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import '../../core/di/injection_container.dart' as di;
import '../../core/utils/app_colors.dart';
import '../../l10n/app_localizations.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_image_view.dart';
import '../../widgets/custom_input_field.dart';
import './bloc/landing_bloc.dart';
import './widgets/app_store_button_widget.dart';
import './widgets/feature_item_widget.dart';
import './widgets/hero_section_widget.dart';
import './widgets/partner_logo_widget.dart';
import './widgets/pricing_card_widget.dart';

class LandingScreen extends StatelessWidget {
  const LandingScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<LandingBloc>(create: (context) => di.sl<LandingBloc>()..add(LandingInitialEvent()), child: const LandingScreen());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LandingBloc, LandingState>(
      builder: (context, state) {
        return Container(
          color: Theme.of(context).colorScheme.surface,
          child: SingleChildScrollView(
            primary: true,
            child: Container(
              width: double.maxFinite,
              constraints: BoxConstraints(maxWidth: 375.h),
              child: Column(
                children: [
                  _buildHeaderSection(context, state),
                  HeroSectionWidget(state: state),
                  _buildPartnersSection(context, state),
                  _buildFeaturesSection(context, state),
                  _buildCtaSection(context, state),
                  _buildDivider(),
                  _buildPricingSection(context, state),
                  _buildDivider(),
                  _buildNewsletterSection(context, state),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeaderSection(BuildContext context, LandingState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 20.h),
      child: Column(
        children: [
          SizedBox(height: 60.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 8.h),
            decoration: BoxDecoration(color: appTheme.colorFFF9F5FF, borderRadius: BorderRadius.circular(15.h)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 12.h, vertical: 4.h),
                      decoration: BoxDecoration(color: appTheme.whiteCustom, borderRadius: BorderRadius.circular(11.h)),
                      child: Text(
                        AppLocalizations.of(context)?.newFeature ?? 'New',
                        style: TextStyleHelper.instance.body12Medium.copyWith(color: appTheme.colorFF6840C6),
                      ),
                    ),
                    SizedBox(width: 8.h),
                    Text('Personalized coaching in-app', style: TextStyleHelper.instance.body12Medium.copyWith(color: AppColors.primaryPurple)),
                  ],
                ),
                CustomImageView(imagePath: ImageConstant.imgArrowrightDeepPurple300, height: 16.h, width: 16.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPartnersSection(BuildContext context, LandingState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 32.h),
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Column(
        children: [
          Text('Official partner of these companies', style: TextStyleHelper.instance.title16Medium.copyWith(color: AppColors.textSecondary)),
          SizedBox(height: 32.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              PartnerLogoWidget(logoPath: ImageConstant.imgVector),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.imgVectorGreenA700),
            ],
          ),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              PartnerLogoWidget(logoPath: ImageConstant.imgCompanyLogo),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.imgVectorBlueA700_01),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.imgVectorBlack900),
            ],
          ),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              PartnerLogoWidget(logoPath: ImageConstant.imgVectorGray900_03),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.imgVectorBlueA400),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesSection(BuildContext context, LandingState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 32.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Features', style: TextStyleHelper.instance.body14SemiBold.copyWith(color: AppColors.primaryPurple)),
          SizedBox(height: 8.h),
          Text(
            AppLocalizations.of(context)?.unlockYourself ?? 'Unlock yourself',
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: appTheme.colorFF0F1728, height: 1.2),
          ),
          SizedBox(height: 16.h),
          Text(
            'Daily personalized fitness, sleep, and recovery data delivered to you in real time with Untitled. We\'re changing how you move.',
            style: TextStyleHelper.instance.headline18.copyWith(color: AppColors.textSecondary, height: 1.4),
          ),
          SizedBox(height: 48.h),
          Column(
            children:
                state.landingModel?.features
                    .map(
                      (feature) => Padding(
                        padding: EdgeInsets.only(bottom: 48.h),
                        child: FeatureItemWidget(icon: feature.icon, title: feature.title, description: feature.description),
                      ),
                    )
                    .toList() ??
                [],
          ),

          IntrinsicHeight(
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: CustomImageView(imagePath: ImageConstant.phone_mockup2, fit: BoxFit.cover, height: 360.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCtaSection(BuildContext context, LandingState state) {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.symmetric(horizontal: 16.h),
      padding: EdgeInsets.only(left: 16.h, right: 16.h, top: 40.h),
      decoration: BoxDecoration(color: appTheme.colorFF52379E, borderRadius: BorderRadius.circular(16.h)),
      child: Column(
        children: [
          Text(
            AppLocalizations.of(context)?.startFreeTrial ?? 'Start your free trial',
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: appTheme.whiteCustom, height: 1.2),
          ),
          SizedBox(height: 16.h),
          Text(
            'Personal performance tracking made easy.',
            style: TextStyleHelper.instance.headline18.copyWith(color: appTheme.colorFFE9D7FE, height: 1.4),
          ),
          SizedBox(height: 32.h),
          Row(
            children: [
              Expanded(
                child: AppStoreButtonWidget(
                  backgroundColor: appTheme.transparentCustom,
                  borderColor: appTheme.whiteCustom,
                  appleLogoPath: ImageConstant.imgAppleLogo,
                  downloadTextPath: ImageConstant.imgDownloadOnThe,
                  storeTextPath: ImageConstant.imgAppStore,
                  onPressed: () {
                    context.read<LandingBloc>().add(CtaAppStoreButtonPressedEvent());
                  },
                ),
              ),
              SizedBox(width: 16.h),
              Expanded(
                child: AppStoreButtonWidget(
                  backgroundColor: appTheme.transparentCustom,
                  borderColor: appTheme.whiteCustom,
                  appleLogoPath: ImageConstant.imgGooglePlayLogoWhiteA700,
                  downloadTextPath: ImageConstant.imgGetItOn,
                  storeTextPath: ImageConstant.imgGooglePlay,
                  onPressed: () {
                    context.read<LandingBloc>().add(CtaPlayStoreButtonPressedEvent());
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 32.h),
          IntrinsicHeight(
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: CustomImageView(imagePath: ImageConstant.phone_mockup3, fit: BoxFit.cover, height: 280.h),
            ),
          ),
          // PhoneMockupWidget(
          //   backgroundImage: null,
          //   screenImage: ImageConstant.imgScreenMockupReplaceFill500x240,
          //   phoneFrameImage: null,
          //   signalIcon: ImageConstant.img941Black900,
          //   batteryIcon: ImageConstant.imgRightBlack900,
          //   height: 500.h,
          //   showComplexFrame: true,
          // ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1.h,
      margin: EdgeInsets.symmetric(horizontal: 16.h),
      color: AppColors.dividerGray,
    );
  }

  Widget _buildPricingSection(BuildContext context, LandingState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 64.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Upgrade', style: TextStyleHelper.instance.body14SemiBold.copyWith(color: AppColors.primaryPurple)),
          SizedBox(height: 8.h),
          Text(
            'Pricing plans that scale with you',
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: AppColors.primaryPurpleDark, height: 1.3),
          ),
          SizedBox(height: 24.h),
          Text(
            'Simple, transparent pricing that grows with you. Try any plan free for 30 days.',
            style: TextStyleHelper.instance.headline18.copyWith(color: AppColors.textSecondary, height: 1.4),
          ),
          SizedBox(height: 32.h),
          Column(
            children:
                state.landingModel?.pricingPlans
                    .map(
                      (plan) => Padding(
                        padding: EdgeInsets.only(bottom: 32.h),
                        child: PricingCardWidget(
                          price: plan.price,
                          title: plan.title,
                          description: plan.description,
                          features: plan.features,
                          isPopular: plan.isPopular,
                          onGetStarted: () {
                            context.read<LandingBloc>().add(PricingPlanSelectedEvent(plan));
                          },
                        ),
                      ),
                    )
                    .toList() ??
                [],
          ),
        ],
      ),
    );
  }

  Widget _buildNewsletterSection(BuildContext context, LandingState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 64.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Be the first to know when we launch',
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: appTheme.colorFF0F1728, height: 1.3),
          ),
          SizedBox(height: 16.h),
          Text(
            'We\'re still building. Subscribe for updates and 20% off when we launch.',
            style: TextStyleHelper.instance.headline18.copyWith(color: AppColors.textSecondary, height: 1.4),
          ),
          SizedBox(height: 32.h),
          CustomInputField(
            hintText: 'Enter your email',
            controller: state.emailController,
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) {
              context.read<LandingBloc>().add(EmailChangedEvent(value));
            },
          ),
          SizedBox(height: 16.h),
          RichText(
            text: TextSpan(
              style: TextStyleHelper.instance.body14.copyWith(color: AppColors.textSecondary),
              children: [
                TextSpan(text: 'We care about your data in our '),
                TextSpan(
                  text: 'privacy policy',
                  style: TextStyle(decoration: TextDecoration.underline),
                  recognizer: TapGestureRecognizer()..onTap = () {},
                ),
              ],
            ),
          ),
          SizedBox(height: 16.h),
          CustomButton(
            text: 'Subscribe',
            onPressed: () {
              context.read<LandingBloc>().add(SubscribeButtonPressedEvent());
            },
          ),
          SizedBox(height: 64.h),
          IntrinsicHeight(
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: CustomImageView(imagePath: ImageConstant.phone_mockup4, fit: BoxFit.cover, height: 360.h),
            ),
          ),
        ],
      ),
    );
  }
}
