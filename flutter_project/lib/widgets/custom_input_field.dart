import 'package:flutter/material.dart';

import '../core/app_export.dart';
import '../core/utils/app_dimensions.dart';

/// A reusable input field component with comprehensive customization options.
///
/// Provides consistent styling, validation support, and accessibility features
/// across the application. Handles various input types including text, email,
/// password, and multiline text with proper visual feedback and error states.
///
/// Features:
/// - Configurable input types and validation
/// - Optional prefix and suffix icons with proper spacing
/// - Multiline support with dynamic height adjustment
/// - Theme-aware styling using semantic dimensions
/// - Accessibility support and proper focus management
/// - Error state handling and visual feedback
class CustomInputField extends StatelessWidget {
  const CustomInputField({
    super.key,
    this.hintText,
    this.controller,
    this.keyboardType,
    this.validator,
    this.obscureText,
    this.enabled,
    this.maxLines,
    this.textInputAction,
    this.onChanged,
    this.onTap,
    this.prefixIcon,
    this.suffixIcon,
  });

  /// Placeholder text to display when field is empty
  final String? hintText;

  /// TextEditingController to manage the input text
  final TextEditingController? controller;

  /// Type of keyboard to display (email, text, number, etc.)
  final TextInputType? keyboardType;

  /// Function to validate the input text
  final String? Function(String?)? validator;

  /// Whether to hide the input text (for passwords)
  final bool? obscureText;

  /// Whether the input field is enabled or disabled
  final bool? enabled;

  /// Maximum number of lines for the input field
  final int? maxLines;

  /// Action to perform when the user finishes editing the text
  final TextInputAction? textInputAction;

  /// Callback function when text changes
  final Function(String)? onChanged;

  /// Callback function when field is tapped
  final VoidCallback? onTap;

  /// Widget to display at the beginning of the field
  final Widget? prefixIcon;

  /// Widget to display at the end of the field
  final Widget? suffixIcon;

  @override
  Widget build(BuildContext context) {
    // Adjust vertical padding when prefixIcon is present to ensure proper text alignment
    final bool hasPrefixIcon = prefixIcon != null;
    final double verticalPadding = maxLines != null && maxLines! > 1
        ? AppDimensions.inputFieldVerticalPadding.h
        : hasPrefixIcon
        ? AppDimensions
              .inputFieldVerticalPadding
              .h // Add vertical padding when prefixIcon is present
        : 0;

    return Container(
      height: maxLines != null && maxLines! > 1 ? null : AppDimensions.inputFieldDefaultHeight.h,
      decoration: BoxDecoration(
        border: Border.all(color: appTheme.colorFFE0E0, width: AppDimensions.inputFieldBorderWidth),
        borderRadius: BorderRadius.circular(AppDimensions.inputFieldBorderRadius.h),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType ?? TextInputType.text,
        validator: validator,
        obscureText: obscureText ?? false,
        enabled: enabled ?? true,
        textInputAction: textInputAction,
        maxLines: maxLines ?? AppDimensions.inputFieldDefaultMaxLines,
        onChanged: onChanged,
        onTap: onTap,
        style: TextStyleHelper.instance.title16Regular,
        decoration: InputDecoration(
          hintText: hintText ?? "Enter text",
          hintStyle: TextStyleHelper.instance.title16Regular,
          prefixIcon: hasPrefixIcon
              ? Container(
                  width: AppDimensions.inputFieldPrefixIconSize.h, // Reduced from 48.h to bring icon closer to text
                  height: AppDimensions.inputFieldDefaultHeight.h,
                  margin: EdgeInsets.only(left: AppDimensions.inputFieldHorizontalPadding.h),
                  alignment: Alignment.center,
                  child: prefixIcon,
                )
              : null,
          prefixIconConstraints: hasPrefixIcon
              ? BoxConstraints(
                  minWidth: AppDimensions.inputFieldPrefixIconSize.h, // Constrain the prefix icon area
                  maxWidth: AppDimensions.inputFieldPrefixIconSize.h,
                  minHeight: AppDimensions.inputFieldDefaultHeight.h,
                  maxHeight: AppDimensions.inputFieldDefaultHeight.h,
                )
              : null,
          suffixIcon: suffixIcon,
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppDimensions.inputFieldHorizontalPadding.h, // Reduce horizontal padding when prefixIcon is present
            vertical: verticalPadding,
          ),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
        ),
      ),
    );
  }
}
